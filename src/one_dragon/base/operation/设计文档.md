# OneDragon Operation & Application 架构设计文档

## 1. 概述

OneDragon 的 Operation 和 Application 模块构成了整个框架的核心执行引擎，采用基于状态机的操作流程控制和分层应用管理架构，为复杂的游戏自动化任务提供了强大而灵活的执行框架。

## 2. 核心设计理念

### 2.1 分层架构
- **OperationBase**: 最基础的操作抽象层
- **Operation**: 具体的操作实现层，支持节点图执行
- **Application**: 应用层，封装完整的业务逻辑
- **OneDragonApp**: 一条龙应用层，支持多应用编排和调度

### 2.2 状态机驱动
- 基于节点图的状态转换机制
- 支持条件分支和循环控制
- 内置重试和错误处理机制

### 2.3 事件驱动
- 全局事件总线机制
- 支持异步事件处理
- 松耦合的组件通信

## 3. 核心组件详解

### 3.1 OperationBase (操作基类)

**职责**: 定义所有操作的基本接口和结果结构

**核心类**:
```python
class OperationResult:
    success: bool      # 执行结果
    status: str        # 状态描述
    data: Any          # 返回数据

class OperationBase:
    def execute(self) -> OperationResult
    def op_success(status, data) -> OperationResult
    def op_fail(status, data) -> OperationResult
```

**设计特点**:
- 统一的结果返回格式
- 简洁的成功/失败状态表示
- 支持任意类型的数据返回

### 3.2 Operation (操作执行引擎)

**职责**: 实现基于节点图的复杂操作流程控制

**核心属性**:
- `ctx: OneDragonContext` - 全局上下文
- `_node_map: dict[str, OperationNode]` - 节点映射
- `_node_edges_map: dict[str, list[OperationEdge]]` - 边映射
- `_current_node: OperationNode` - 当前执行节点

**执行流程**:
1. **初始化阶段**: 构建节点图，确定起始节点
2. **执行循环**: 按节点图执行，处理状态转换
3. **结果处理**: 根据最终状态返回操作结果

**关键方法**:
- `_init_network()`: 构建操作节点网络
- `_execute_one_round()`: 执行单轮操作
- `_get_next_node()`: 根据结果选择下一个节点

### 3.3 OperationNode (操作节点)

**职责**: 表示操作流程中的单个执行步骤

**核心属性**:
```python
class OperationNode:
    cn: str                    # 节点名称
    func: Callable             # 节点处理函数
    op_method: Callable        # 类方法处理函数
    op: OperationBase          # 子操作
    retry_on_op_fail: bool     # 失败时是否重试
    node_max_retry_times: int  # 最大重试次数
    timeout_seconds: float     # 超时时间
```

**注解支持**:
```python
@operation_node(name='节点名称', is_start_node=True)
def node_method(self) -> OperationRoundResult:
    # 节点逻辑实现
    return self.round_success()
```

### 3.4 OperationEdge (操作边)

**职责**: 定义节点间的连接关系和转换条件

**核心属性**:
```python
class OperationEdge:
    node_from: OperationNode   # 源节点
    node_to: OperationNode     # 目标节点
    success: bool              # 成功条件
    status: str                # 状态匹配条件
    ignore_status: bool        # 是否忽略状态
```

**注解支持**:
```python
@node_from(from_name='源节点', status='特定状态')
@operation_node(name='目标节点')
def target_node(self) -> OperationRoundResult:
    # 目标节点逻辑
```

### 3.5 OperationRoundResult (轮次结果)

**职责**: 表示单轮操作的执行结果

**结果类型**:
```python
class OperationRoundResultEnum(Enum):
    RETRY = 0    # 重试
    SUCCESS = 1  # 成功
    WAIT = 2     # 等待
    FAIL = -1    # 失败
```

**便捷方法**:
- `round_success()`: 创建成功结果
- `round_fail()`: 创建失败结果
- `round_retry()`: 创建重试结果
- `round_wait()`: 创建等待结果

### 3.6 Application (应用基类)

**职责**: 封装完整的业务逻辑，提供应用级别的生命周期管理

**核心功能**:
- 运行记录管理
- 上下文生命周期控制
- 通知机制
- OCR 初始化

**关键属性**:
```python
class Application(Operation):
    app_id: str                        # 应用唯一标识
    run_record: AppRunRecord           # 运行记录
    init_context_before_start: bool    # 是否初始化上下文
    stop_context_after_stop: bool     # 是否停止上下文
    need_ocr: bool                     # 是否需要OCR
    need_notify: bool                  # 是否需要通知
```

**生命周期方法**:
- `_init_before_execute()`: 执行前初始化
- `after_operation_done()`: 执行后清理
- `init_for_application()`: 应用特定初始化

### 3.7 AppRunRecord (应用运行记录)

**职责**: 管理应用的运行状态和历史记录

**状态定义**:
```python
STATUS_WAIT = 0     # 等待运行
STATUS_SUCCESS = 1  # 运行成功
STATUS_FAIL = 2     # 运行失败
STATUS_RUNNING = 3  # 正在运行
```

**核心功能**:
- 基于时间的状态重置（日/周）
- 运行状态跟踪
- 配置持久化

**关键方法**:
- `check_and_update_status()`: 检查并更新状态
- `reset_record()`: 重置运行记录
- `run_status_under_now`: 获取当前有效状态

### 3.8 OneDragonApp (一条龙应用)

**职责**: 实现多应用的编排和调度，支持多实例运行

**核心功能**:
- 多应用顺序执行
- 失败应用重试机制
- 多实例账号切换
- 应用状态管理

**关键属性**:
```python
class OneDragonApp(Application):
    _to_run_app_list: List[Application]    # 待运行应用列表
    _current_app_idx: int                  # 当前应用索引
    _instance_list: List[OneDragonInstance] # 实例列表
    _instance_idx: int                     # 当前实例索引
    _fail_app_idx: List[int]               # 失败应用索引
```

**执行流程**:
1. **检测任务状态**: 确定需要运行的应用
2. **运行任务**: 按顺序执行应用
3. **重试失败任务**: 重新执行失败的应用
4. **切换实例配置**: 切换到下一个实例
5. **切换账号**: 执行账号切换操作
6. **切换账号后处理**: 判断是否继续或结束

### 3.9 OneDragonContext (全局上下文)

**职责**: 提供全局状态管理和资源访问

**核心功能**:
- 组件生命周期管理
- 事件总线机制
- 配置管理
- 资源初始化

**运行状态**:
```python
class ContextRunStateEnum(Enum):
    STOP = 0   # 停止
    RUN = 1    # 运行
    PAUSE = 2  # 暂停
```

### 3.10 ContextEventBus (事件总线)

**职责**: 实现组件间的松耦合通信

**核心功能**:
- 事件注册和分发
- 异步事件处理
- 线程安全的事件机制

## 4. 设计模式应用

### 4.1 状态机模式
- 节点表示状态，边表示状态转换
- 支持复杂的条件分支逻辑
- 内置重试和错误处理

### 4.2 模板方法模式
- Operation 定义执行框架
- 子类实现具体的节点逻辑
- 统一的生命周期管理

### 4.3 观察者模式
- 事件总线实现组件解耦
- 支持多个监听器
- 异步事件处理

### 4.4 策略模式
- 不同的节点处理策略
- 可插拔的操作实现
- 灵活的执行路径选择

## 5. 关键特性

### 5.1 容错性
- 多层次的重试机制
- 超时保护
- 异常捕获和处理

### 5.2 可扩展性
- 基于注解的节点定义
- 插件化的应用架构
- 灵活的配置系统

### 5.3 可观测性
- 详细的执行日志
- 运行状态跟踪
- 性能监控支持

### 5.4 并发安全
- 线程安全的上下文管理
- 异步事件处理
- 资源竞争保护

## 6. 使用示例

### 6.1 简单操作定义
```python
class SimpleOperation(Operation):
    @operation_node(name='开始', is_start_node=True)
    def start(self) -> OperationRoundResult:
        return self.round_success('开始完成')
    
    @node_from(from_name='开始')
    @operation_node(name='结束')
    def end(self) -> OperationRoundResult:
        return self.round_success('操作完成')
```

### 6.2 应用定义
```python
class MyApplication(Application):
    def __init__(self, ctx: OneDragonContext):
        super().__init__(ctx, 'my_app', op_name='我的应用')
    
    @operation_node(name='主逻辑', is_start_node=True)
    def main_logic(self) -> OperationRoundResult:
        # 应用主要逻辑
        return self.round_success()
```

## 7. 最佳实践

### 7.1 节点设计
- 保持节点逻辑简单和专一
- 合理使用重试机制
- 设置适当的超时时间

### 7.2 状态管理
- 明确定义状态转换条件
- 避免复杂的状态依赖
- 使用有意义的状态名称

### 7.3 错误处理
- 区分可重试和不可重试的错误
- 提供详细的错误信息
- 实现优雅的降级策略

### 7.4 性能优化
- 避免不必要的截图操作
- 合理设置等待时间
- 使用异步处理提高并发性

## 8. 总结

OneDragon 的 Operation 和 Application 架构通过分层设计、状态机驱动和事件机制，构建了一个强大而灵活的游戏自动化执行框架。该架构不仅支持复杂的业务逻辑实现，还提供了良好的扩展性和维护性，为游戏自动化任务的开发提供了坚实的基础。
