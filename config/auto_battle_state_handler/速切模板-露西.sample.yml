template_name: "速切模板-露西"
handlers:
  - states: "[前台-露西]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "露西-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[露西-终结技可用]"
        operations:
          - op_name: "设置状态"
            state: "自定义-露西-加油"
          - operation_template: "通用-终结技"
          - op_name: "设置状态"
            data: ["自定义-速切结束"]

      - states: "[露西-特殊技可用]"
        sub_handlers:
          - states: "[自定义-连携换人, 0, 10]"
            debug_name: "失衡期快速发球"
            operations:
              - operation_template: "露西-平直球合轴"

          # 高飞球
          - states: ""
            debug_name: "非失衡期打高飞球"
            operations:
              - operation_template: "露西-高飞球合轴"

      - states: ""
        debug_name: "打不了球打普攻"
        operations:
          - operation_template: "通用-切人普通攻击"