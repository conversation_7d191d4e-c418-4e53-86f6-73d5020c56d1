template_name: "速切模板-安东"
handlers:
  - states: "[前台-安东]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "安东-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "清除状态"
                state: "自定义-安东-爆发状态"
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "清除状态"
                state: "自定义-安东-爆发状态"
              - op_name: "等待秒数"
                seconds: 0.3

      # 失衡时刻
      - states: "[自定义-连携换人, 0, 10] | [自定义-感电, 0, 12]"  # 感电暂时不可用
        sub_handlers:
          # 失衡的时候就不用考虑这么多了，射吧
          - states: "[安东-特殊技可用] & (![自定义-安东-爆发状态, 0, 12] | ([按键-切换角色-下一个, 0, 1]|[按键-切换角色-上一个, 0, 1]))"
            operations:
              - op_name: "设置状态"
                state: "自定义-动作不打断"
                seconds: 1
              - op_name: "按键-特殊攻击"
                post_delay: 0.2
                repeat: 4
              - op_name: "设置状态"
                state: "自定义-安东-爆发状态"

          - states: "[安东-终结技可用]"
            operations:
              - operation_template: "通用-终结技"

          - states: "[自定义-安东-爆发状态, 0, 14]"
            operations:
              # 打桩打桩
              - op_name: "按键-普通攻击"
                post_delay: 0.1
                repeat: 25

      - states: "![自定义-连携换人, 0, 10]"
        sub_handlers:
          - states: "[安东-能量]{110, 120}"
            operations:
              - op_name: "按键-特殊攻击"
                post_delay: 0.3
                repeat: 2
              - op_name: "设置状态"
                data: ["自定义-速切结束"]
          - states: ""
            operations:
              - op_name: "按键-普通攻击"
                post_delay: 0.1
                repeat: 25
              - op_name: "设置状态"
                data: ["自定义-速切结束"]