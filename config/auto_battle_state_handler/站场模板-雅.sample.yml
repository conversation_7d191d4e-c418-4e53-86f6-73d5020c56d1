handlers:
  - states: "[前台-雅]"
    sub_handlers:
      - states: "[按键-切换角色-下一个, 0, 0.8]"
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 8
      - states: "[雅-落霜]{6, 6}"
        operations:
          - op_name: "设置状态"
            state: "自定义-星见雅使用过拔刀斩"
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-普通攻击"
            way: "按下"
            post_delay: 2
          - op_name: "清除状态"
            state: "自定义-动作不打断"
          - op_name: "等待秒数"
            seconds: 1
      - states: "[按键可用-终结技] & ![自定义-星见雅使用过拔刀斩, 0, 5] & [雅-落霜]{0, 3}"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-终结技"
            post_delay: 1
            repeat: 5
          - op_name: "清除状态"
            state: "自定义-动作不打断"
      - states: "[按键可用-特殊攻击]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-特殊攻击"
            post_delay: 0.5
            repeat: 2
          - op_name: "清除状态"
            state: "自定义-动作不打断"
      # 普通攻击
      - states: ""
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 25