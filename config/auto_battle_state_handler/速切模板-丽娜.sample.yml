template_name: "速切模板-丽娜"
handlers:
  - states: "[前台-丽娜]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"  # 丽娜的连携特别快
        operations:
          - op_name: "设置状态"
            state: "自定义-丽娜-人偶"
            seconds: 14
          - operation_template: "通用-切人普通攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - op_name: "设置状态"
            state: "自定义-丽娜-人偶"
            seconds: 12
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"  # 丽娜的连携特别快
        operations:
          - op_name: "设置状态"
            state: "自定义-丽娜-人偶"
            seconds: 14
          - operation_template: "通用-切人普通攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[丽娜-终结技可用]"
        operations:
          - op_name: "设置状态"
            state: "自定义-丽娜-人偶"
            seconds: 17
          - operation_template: "通用-终结技"
          - operation_template: "通用-切人普通攻击"

      - states: ""
        operations:
          - op_name: "设置状态"
            state: "自定义-丽娜-人偶"
            seconds: 14
          - op_name: "按键-特殊攻击"
            post_delay: 0.1
            repeat: 4
          - operation_template: "通用-切人普通攻击"