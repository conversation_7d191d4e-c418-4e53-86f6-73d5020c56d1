template_name: "速切模板-艾莲"
handlers:
  - states: "[前台-艾莲]"
    sub_handlers:
      # 黄光操作
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "艾莲-支援攻击"
      # 红光操作
      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          # 设置快速蓄力状态
          - op_name: "设置状态"
            state: "[自定义-艾莲-快速蓄力]"
          - operation_template: "艾莲-闪A"
      # 连携操作
      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "艾莲-连携攻击"
      # 清状态
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "设置状态"
                state: "[自定义-艾莲-快速蓄力]"
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      # 失衡时刻
      - states: "[自定义-连携换人, 0, 10]"
        debug_name: "失衡期间"
        sub_handlers:
          # 失衡阶段：Q
          - states: "[艾莲-终结技可用]"
            operations:
              - operation_template: "艾莲-终结技"

          # 失衡阶段 EE2连
          - states: "[艾莲-能量]{80, 120}"
            operations:
              - operation_template: "艾莲-新双E连招"

      # 非失衡时刻，就是鲨鱼妹的站场时间
      - states: ""
        debug_name: "非失衡期间"
        sub_handlers:
          # 失衡阶段 EE2连
          - states: "[艾莲-能量]{120, 120}"
            operations:
              - operation_template: "艾莲-新双E连招"

          # 快速
          - states: "[自定义-艾莲-快速蓄力,0,12]"
            operations:
              - operation_template: "艾莲-新冲刺剪刀一套快速"
              # 清除状态
              - op_name: "清除状态"
                state: "[自定义-艾莲-快速蓄力]"

          # 慢速
          - states: ""
            debug_name: "没有快速蓄力"
            operations:
              - operation_template: "艾莲-新冲刺剪刀一套慢速"
