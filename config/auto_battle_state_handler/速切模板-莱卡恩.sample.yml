template_name: "速切模板-莱卡恩"
handlers:
  - states: "[前台-莱卡恩]"
    sub_handlers:
      # 支援
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "莱卡恩-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "莱卡恩-闪A"

      # 连携
      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.3]"
            operations:
              - op_name: "等待秒数"
                seconds: 0.3
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      # 失衡时刻
      - states: "[自定义-连携换人, 0, 10]"
        operations:
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - operation_template: "莱卡恩-普通攻击"

      # 终结技时刻，需要注意出场第一秒可能识别错误
      - states: "[莱卡恩-终结技可用] & ![自定义-连携换人, 0, 10]"
        operations:
          - operation_template: "莱卡恩-终结技"

      - states: "[莱卡恩-特殊技可用]"
        operations:
          - operation_template: "莱卡恩-特殊技合轴"
          - op_name: "设置状态"
            state: "自定义-莱卡恩衔接长按蓄力"

      - states: "[自定义-莱卡恩衔接长按蓄力, 0, 1]"
        operations:
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - operation_template: "莱卡恩-蓄力普通攻击"
          - op_name: "设置状态"
            state: "自定义-莱卡恩衔接长按蓄力A"

      - states: ""
        operations:
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - operation_template: "莱卡恩-普通攻击"
          - op_name: "设置状态"
            state: "自定义-莱卡恩衔接长按蓄力A"